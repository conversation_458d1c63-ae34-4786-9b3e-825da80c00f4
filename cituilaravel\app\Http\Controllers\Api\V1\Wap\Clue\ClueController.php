<?php
declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Wap\Clue;

use App\Http\Controllers\Api\Controller;
use App\Service\Clue\ClueService;
use App\Service\App\AppService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ClueController extends Controller
{
    protected ClueService $clueService;
    protected AppService $appService;

    public function __construct(ClueService $clueService, AppService $appService)
    {
        $this->clueService = $clueService;
        $this->appService = $appService;
        parent::__construct();
    }

    /**
     * 获取APP列表（用于下拉选择）
     * @return JsonResponse
     */
    public function getAppList(): JsonResponse
    {
        $data = $this->appService->getAppListForSelect();
        return $this->apiSuccess($data);
    }

    /**
     * 提交线索
     * @param Request $request
     * @return JsonResponse
     */
    public function submitClue(Request $request): JsonResponse
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'app_id' => 'required|integer|min:1|exists:apps,id',
            'release_time' => 'nullable|integer|min:0',
            'clue_money' => 'nullable|numeric|min:0|max:999999.99',
            'package_amount' => 'required|string|in:0.1-0.29,0.3-0.49,0.5-0.99,1以上',
            'device_model' => 'required|string|min:1|max:100',
            'pic_tixian' => 'required|string|min:1|max:255',
            'pic_daozhang' => 'required|string|min:1|max:255',
            'nick_name' => 'required|string|min:1|max:100',
            'clue_description' => 'required|string|min:1|max:300'
        ], [
            'app_id.required' => '请选择APP',
            'app_id.integer' => 'APP ID格式错误',
            'app_id.min' => '请选择有效的APP',
            'app_id.exists' => '选择的APP不存在',
            'release_time.integer' => '放水时间格式错误',
            'release_time.min' => '放水时间不能为负数',
            'clue_money.numeric' => '放水金额格式错误',
            'clue_money.min' => '放水金额不能为负数',
            'clue_money.max' => '放水金额不能超过999999.99元',
            'package_amount.required' => '请选择单包大小',
            'package_amount.in' => '单包大小选择无效',
            'device_model.required' => '请输入设备型号',
            'device_model.min' => '设备型号不能为空',
            'device_model.max' => '设备型号不能超过100个字符',
            'pic_tixian.required' => '请上传APP提现记录截图',
            'pic_tixian.min' => 'APP提现记录截图不能为空',
            'pic_tixian.max' => 'APP提现记录截图路径过长',
            'pic_daozhang.required' => '请上传微信到账记录截图',
            'pic_daozhang.min' => '微信到账记录截图不能为空',
            'pic_daozhang.max' => '微信到账记录截图路径过长',
            'nick_name.required' => '请输入发布人姓名',
            'nick_name.min' => '发布人姓名不能为空',
            'nick_name.max' => '发布人姓名不能超过100个字符',
            'clue_description.required' => '请填写线索描述',
            'clue_description.min' => '线索描述不能为空',
            'clue_description.max' => '线索描述不能超过300个字符'
        ]);

        if ($validator->fails()) {
            return $this->apiError($validator->errors()->first());
        }

        $data = $this->clueService->submitClue($request->all());
        return $this->apiSuccess($data,200, '线索提交成功');

    }

    /**
     * 获取用户的线索列表
     * @return JsonResponse
     */
    public function getUserClues(): JsonResponse
    {
        $data = $this->clueService->getUserClues();
        return $this->apiSuccess($data);
    }

    /**
     * 获取线索列表（公开接口，无需登录）
     * @return JsonResponse
     */
    public function getClueList(): JsonResponse
    {
        $data = $this->clueService->getClueList();
        return $this->apiSuccess($data);
    }

    /**
     * 获取指定APP的放水线索列表（公开接口，无需登录）
     * @return JsonResponse
     */
    public function getAppClues(): JsonResponse
    {
        $data = $this->clueService->getAppClues();
        return $this->apiSuccess($data);
    }
}
