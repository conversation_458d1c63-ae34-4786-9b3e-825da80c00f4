// token存储的key
const TOKEN_KEY = 'auth_token'

/**
 * 获取token
 */
export const getToken = () => {
    return uni.getStorageSync(TOKEN_KEY) || ''
}

/**
 * 设置token
 * @param {string} token
 */
export const setToken = (token) => {
    uni.setStorageSync(TOKEN_KEY, token)
}

/**
 * 移除token
 */
export const removeToken = () => {
    uni.removeStorageSync(TOKEN_KEY)
}

/**
 * 检查用户是否已登录
 * @returns {boolean}
 */
export const isLoggedIn = () => {
    const token = getToken()
    const userInfo = uni.getStorageSync('userInfo')
    return !!(token && userInfo)
}

/**
 * 检查用户是否为管理员
 * @returns {boolean}
 */
export const isAdmin = () => {
    if (!isLoggedIn()) {
        return false
    }

    const userInfo = uni.getStorageSync('userInfo')

    if (userInfo && userInfo.is_admin === 1) {
        return true
    }

    return false
}

/**
 * 检查管理员权限，如果不满足则跳转到登录页
 * @param {boolean} showToast 是否显示提示信息
 * @returns {boolean} 是否有管理员权限
 */
export const checkAdminPermission = (showToast = true) => {
    if (!isLoggedIn()) {
        if (showToast) {
            uni.showToast({
                title: '请先登录',
                icon: 'none',
                duration: 2000
            })
        }
        setTimeout(() => {
            uni.reLaunch({
                url: '/pages/login/login'
            })
        }, showToast ? 2000 : 0)
        return false
    }

    if (!isAdmin()) {
        if (showToast) {
            uni.showToast({
                title: '您没有权限访问此功能',
                icon: 'none',
                duration: 2000
            })
        }
        setTimeout(() => {
            uni.reLaunch({
                url: '/pages/login/login'
            })
        }, showToast ? 2000 : 0)
        return false
    }

    return true
}

/**
 * 获取当前用户信息
 * @returns {object|null}
 */
export const getCurrentUser = () => {
    if (!isLoggedIn()) {
        return null
    }
    return uni.getStorageSync('userInfo')
}