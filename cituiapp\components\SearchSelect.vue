<template>
	<view class="search-select">
		<!-- 选择器触发区域 -->
		<view class="select-container" @click="showPicker">
			<view class="select-input" :class="{ 'placeholder': !selectedText }">
				{{ selectedText || placeholder }}
			</view>
			<u-icon name="arrow-down" size="16" color="#999"></u-icon>
		</view>

		<!-- 弹出层 -->
		<u-popup
			:show="show"
			mode="bottom"
			:border-radius="20"
			@close="closePicker"
		>
			<view class="popup-container">
				<!-- 标题栏 -->
				<view class="popup-header">
					<view class="popup-title">{{ title }}</view>
					<view class="popup-close" @click="closePicker">
						<u-icon name="close" size="20" color="#666"></u-icon>
					</view>
				</view>

				<!-- 搜索框 -->
				<view class="search-container">
					<u-input
						v-model="searchKeyword"
						placeholder="输入关键词搜索..."
						border="surround"
						:clearable="true"
						@input="onSearchInput"
						@clear="onSearchClear"
					></u-input>
				</view>
				
				<!-- 选项列表 -->
				<scroll-view class="options-container" scroll-y>
					<view v-if="filteredOptions.length === 0" class="empty-state">
						<text class="empty-text">{{ searchKeyword ? '未找到匹配的选项' : '暂无数据' }}</text>
					</view>
					<view 
						v-for="(option, index) in filteredOptions" 
						:key="option.value || index"
						class="option-item"
						:class="{ 'selected': option.value === selectedValue }"
						@click="selectOption(option)"
					>
						<view class="option-content">
							<text class="option-text">{{ option.text }}</text>
							<text v-if="option.subtitle" class="option-subtitle">{{ option.subtitle }}</text>
						</view>
						<u-icon 
							v-if="option.value === selectedValue" 
							name="checkmark" 
							size="18" 
							color="#dc2626"
						></u-icon>
					</view>
				</scroll-view>
				
				<!-- 底部按钮 -->
				<view class="popup-footer">
					<view class="button-group">
						<u-button
							type="default"
							size="large"
							@click="closePicker"
							class="cancel-btn"
						>
							取消
						</u-button>
						<u-button
							type="primary"
							size="large"
							@click="confirmSelection"
							:disabled="!selectedValue"
							class="confirm-btn"
						>
							确定
						</u-button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	name: 'SearchSelect',
	props: {
		// 当前选中的值
		value: {
			type: [String, Number],
			default: ''
		},
		// 选项列表
		options: {
			type: Array,
			default: () => []
		},
		// 占位符
		placeholder: {
			type: String,
			default: '请选择'
		},
		// 弹窗标题
		title: {
			type: String,
			default: '请选择'
		},
		// 是否禁用
		disabled: {
			type: Boolean,
			default: false
		}
	},
	
	data() {
		return {
			show: false,
			searchKeyword: '',
			selectedValue: '',
			selectedText: '',
			filteredOptions: []
		}
	},
	
	watch: {
		value: {
			handler(newVal) {
				this.selectedValue = newVal
				this.updateSelectedText()
			},
			immediate: true
		},
		
		options: {
			handler(newVal) {
				this.filteredOptions = [...newVal]
				this.updateSelectedText()
			},
			immediate: true,
			deep: true
		}
	},
	
	methods: {
		// 显示选择器
		showPicker() {
			console.log('SearchSelect showPicker called')
			console.log('disabled:', this.disabled)
			console.log('options length:', this.options.length)

			if (this.disabled) {
				console.log('组件被禁用，不显示选择器')
				return
			}

			// 如果没有选项，显示提示
			if (this.options.length === 0) {
				uni.showToast({
					title: '暂无可选项',
					icon: 'none'
				})
				return
			}

			// 始终使用带搜索功能的弹窗
			this.show = true
			this.searchKeyword = ''
			this.filteredOptions = [...this.options]
		},
		
		// 关闭选择器
		closePicker() {
			this.show = false
			this.searchKeyword = ''
		},
		
		// 搜索输入
		onSearchInput(value) {
			this.filterOptions(value)
		},
		
		// 清空搜索
		onSearchClear() {
			this.searchKeyword = ''
			this.filteredOptions = [...this.options]
		},
		
		// 过滤选项
		filterOptions(keyword) {
			if (!keyword) {
				this.filteredOptions = [...this.options]
				return
			}
			
			const lowerKeyword = keyword.toLowerCase()
			this.filteredOptions = this.options.filter(option => {
				const text = (option.text || '').toLowerCase()
				const subtitle = (option.subtitle || '').toLowerCase()
				return text.includes(lowerKeyword) || subtitle.includes(lowerKeyword)
			})
		},
		
		// 选择选项
		selectOption(option) {
			this.selectedValue = option.value
			this.selectedText = option.text
		},
		
		// 确认选择
		confirmSelection() {
			this.$emit('input', this.selectedValue)
			this.$emit('change', this.selectedValue, this.selectedText)
			this.closePicker()
		},
		
		// 更新选中文本
		updateSelectedText() {
			if (!this.selectedValue) {
				this.selectedText = ''
				return
			}
			
			const selectedOption = this.options.find(option => option.value === this.selectedValue)
			this.selectedText = selectedOption ? selectedOption.text : ''
		}
	},


}
</script>

<style lang="scss" scoped>
.search-select {
	width: 100%;
}

.select-container {
	border: 2rpx solid #d1d5db;
	border-radius: 16rpx;
	background-color: #ffffff;
	padding: 20rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	transition: all 0.2s ease;
	cursor: pointer;
	position: relative;
	z-index: 1;
	height: 88rpx;
	box-sizing: border-box;

	&:active {
		background-color: #f9fafb;
		border-color: #9ca3af;
	}
}

.select-input {
	flex: 1;
	font-size: 28rpx;
	color: #111827;
	
	&.placeholder {
		color: #9ca3af;
	}
}

.popup-container {
	background-color: #fff;
	border-radius: 20rpx 20rpx 0 0;
	max-height: 80vh;
	min-height: 400rpx;
	display: flex;
	flex-direction: column;
}

.popup-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.popup-close {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.search-container {
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.options-container {
	flex: 1;
	max-height: 60vh;
	padding: 16rpx 0;
}

.empty-state {
	padding: 80rpx 32rpx;
	text-align: center;
}

.empty-text {
	font-size: 28rpx;
	color: #9ca3af;
}

.option-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 24rpx 32rpx;
	transition: background-color 0.2s ease;
	
	&:active {
		background-color: #f9fafb;
	}
	
	&.selected {
		background-color: #fef2f2;
	}
}

.option-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.option-text {
	font-size: 28rpx;
	color: #333;
	line-height: 1.4;
}

.option-subtitle {
	font-size: 24rpx;
	color: #666;
	margin-top: 4rpx;
}

.popup-footer {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 24rpx 32rpx;
	border-top: 1rpx solid #f0f0f0;
	background-color: #fff;
}

.button-group {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 24rpx;
	width: 100%;
}

.cancel-btn {
	flex: none;
	width: 220rpx;
	height: 80rpx;
}

.confirm-btn {
	flex: none;
	width: 220rpx;
	height: 80rpx;
}
</style>
